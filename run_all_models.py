#!/usr/bin/env python3
"""
Script to run all wine variety classification models and compare results.

This script executes all three machine learning approaches and provides
a comprehensive comparison of their performance.
"""

import time
import subprocess
import sys
import os
from typing import Dict, Any


def run_model(script_name: str) -> Dict[str, Any]:
    """
    Run a model script and capture its output and execution time.
    
    Args:
        script_name (str): Name of the Python script to run
        
    Returns:
        Dict[str, Any]: Dictionary containing execution results
    """
    print(f"\n{'='*50}")
    print(f"Running {script_name}...")
    print(f"{'='*50}")
    
    start_time = time.time()
    
    try:
        # Run the script and capture output
        result = subprocess.run(
            [sys.executable, script_name],
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        if result.returncode == 0:
            print(f"✅ {script_name} completed successfully")
            print(f"Output: {result.stdout}")
            
            # Try to extract accuracy from output
            accuracy = None
            for line in result.stdout.split('\n'):
                if 'Accuracy:' in line:
                    try:
                        accuracy_str = line.split('Accuracy:')[1].strip().replace('%', '')
                        accuracy = float(accuracy_str)
                        break
                    except (IndexError, ValueError):
                        pass
            
            return {
                'success': True,
                'accuracy': accuracy,
                'execution_time': execution_time,
                'output': result.stdout,
                'error': None
            }
        else:
            print(f"❌ {script_name} failed with return code {result.returncode}")
            print(f"Error: {result.stderr}")
            
            return {
                'success': False,
                'accuracy': None,
                'execution_time': execution_time,
                'output': result.stdout,
                'error': result.stderr
            }
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {script_name} timed out after 5 minutes")
        return {
            'success': False,
            'accuracy': None,
            'execution_time': 300,
            'output': "",
            'error': "Script timed out"
        }
    except Exception as e:
        print(f"💥 Unexpected error running {script_name}: {str(e)}")
        return {
            'success': False,
            'accuracy': None,
            'execution_time': 0,
            'output': "",
            'error': str(e)
        }


def check_prerequisites() -> bool:
    """
    Check if all prerequisites are met before running models.
    
    Returns:
        bool: True if all prerequisites are met, False otherwise
    """
    print("Checking prerequisites...")
    
    # Check if data file exists
    data_file = os.path.join('data', 'wine_data.csv')
    if not os.path.exists(data_file):
        print(f"❌ Dataset not found: {data_file}")
        print("Please place your wine_data.csv file in the data/ directory")
        return False
    
    # Check if model scripts exist
    scripts = ['deep_learning.py', 'naive_bayes.py', 'support_vector.py']
    for script in scripts:
        if not os.path.exists(script):
            print(f"❌ Model script not found: {script}")
            return False
    
    # Check if lib directory exists
    if not os.path.exists('lib'):
        print("❌ lib directory not found")
        return False
    
    print("✅ All prerequisites met")
    return True


def main():
    """Main function to run all models and compare results."""
    print("🍷 Wine Variety Classification - Model Comparison")
    print("=" * 60)
    
    # Check prerequisites
    if not check_prerequisites():
        print("\n❌ Prerequisites not met. Please fix the issues above and try again.")
        sys.exit(1)
    
    # Define models to run
    models = {
        'Deep Learning (CNN)': 'deep_learning.py',
        'Naive Bayes': 'naive_bayes.py',
        'Support Vector Machine': 'support_vector.py'
    }
    
    # Run all models
    results = {}
    total_start_time = time.time()
    
    for model_name, script_name in models.items():
        results[model_name] = run_model(script_name)
    
    total_end_time = time.time()
    total_execution_time = total_end_time - total_start_time
    
    # Display comparison results
    print(f"\n{'='*60}")
    print("📊 RESULTS COMPARISON")
    print(f"{'='*60}")
    
    print(f"{'Model':<25} {'Accuracy':<12} {'Time (s)':<10} {'Status':<10}")
    print("-" * 60)
    
    successful_models = []
    for model_name, result in results.items():
        accuracy_str = f"{result['accuracy']:.2f}%" if result['accuracy'] is not None else "N/A"
        time_str = f"{result['execution_time']:.2f}"
        status = "✅ Success" if result['success'] else "❌ Failed"
        
        print(f"{model_name:<25} {accuracy_str:<12} {time_str:<10} {status:<10}")
        
        if result['success'] and result['accuracy'] is not None:
            successful_models.append((model_name, result['accuracy']))
    
    print("-" * 60)
    print(f"Total execution time: {total_execution_time:.2f} seconds")
    
    # Determine best model
    if successful_models:
        best_model = max(successful_models, key=lambda x: x[1])
        print(f"\n🏆 Best performing model: {best_model[0]} ({best_model[1]:.2f}%)")
    else:
        print("\n❌ No models completed successfully")
    
    # Display any errors
    failed_models = [(name, result) for name, result in results.items() if not result['success']]
    if failed_models:
        print(f"\n⚠️  ERRORS:")
        for model_name, result in failed_models:
            print(f"{model_name}: {result['error']}")
    
    print(f"\n{'='*60}")
    print("Analysis complete! 🎉")
    
    # Save results to file
    try:
        os.makedirs('results', exist_ok=True)
        with open('results/model_comparison.txt', 'w') as f:
            f.write("Wine Variety Classification - Model Comparison Results\n")
            f.write("=" * 60 + "\n\n")
            
            for model_name, result in results.items():
                f.write(f"{model_name}:\n")
                f.write(f"  Success: {result['success']}\n")
                f.write(f"  Accuracy: {result['accuracy']}\n")
                f.write(f"  Execution Time: {result['execution_time']:.2f}s\n")
                if result['error']:
                    f.write(f"  Error: {result['error']}\n")
                f.write("\n")
            
            f.write(f"Total Execution Time: {total_execution_time:.2f}s\n")
            
            if successful_models:
                f.write(f"Best Model: {best_model[0]} ({best_model[1]:.2f}%)\n")
        
        print("Results saved to results/model_comparison.txt")
        
    except Exception as e:
        print(f"Warning: Could not save results to file: {e}")


if __name__ == "__main__":
    main()
