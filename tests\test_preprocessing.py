"""
Unit tests for text preprocessing utilities.
"""

import unittest
import sys
import os

# Add the parent directory to the path to import lib modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from lib.get_top_xwords import clean_text, filter_to_top_x, get_vocabulary_stats


class TestTextPreprocessing(unittest.TestCase):
    """Test cases for text preprocessing functions."""
    
    def setUp(self):
        """Set up test data."""
        self.sample_descriptions = [
            "This wine has a rich, full-bodied flavor with notes of cherry and oak.",
            "A light, crisp wine with citrus undertones and a clean finish.",
            "Bold and complex, featuring dark fruit flavors and spicy notes.",
            "Smooth texture with hints of vanilla and chocolate."
        ]
    
    def test_clean_text(self):
        """Test text cleaning function."""
        # Test normal text
        result = clean_text("Hello, World! 123")
        self.assertEqual(result, "hello world")
        
        # Test empty string
        result = clean_text("")
        self.assertEqual(result, "")
        
        # Test None input
        result = clean_text(None)
        self.assertEqual(result, "")
        
        # Test text with special characters
        result = clean_text("Wine's flavor is great! (2021)")
        self.assertEqual(result, "wines flavor is great")
    
    def test_filter_to_top_x(self):
        """Test vocabulary filtering function."""
        mapped_list, vocab = filter_to_top_x(self.sample_descriptions, vocab_size=20)
        
        # Check that we get the right number of descriptions
        self.assertEqual(len(mapped_list), len(self.sample_descriptions))
        
        # Check that vocabulary is not empty
        self.assertGreater(len(vocab), 0)
        
        # Check that vocabulary size doesn't exceed requested size
        self.assertLessEqual(len(vocab), 20)
        
        # Check that all mapped descriptions contain only valid indices
        for description_indices in mapped_list:
            for idx in description_indices:
                self.assertGreaterEqual(idx, 0)
                self.assertLessEqual(idx, len(vocab))
    
    def test_get_vocabulary_stats(self):
        """Test vocabulary statistics function."""
        stats = get_vocabulary_stats(self.sample_descriptions)
        
        # Check that all expected keys are present
        expected_keys = ['total_words', 'unique_words', 'avg_words_per_description', 'most_common_word']
        for key in expected_keys:
            self.assertIn(key, stats)
        
        # Check that values are reasonable
        self.assertGreater(stats['total_words'], 0)
        self.assertGreater(stats['unique_words'], 0)
        self.assertGreater(stats['avg_words_per_description'], 0)
        self.assertIsInstance(stats['most_common_word'], tuple)
    
    def test_empty_input(self):
        """Test functions with empty input."""
        # Test with empty list
        mapped_list, vocab = filter_to_top_x([])
        self.assertEqual(len(mapped_list), 0)
        self.assertEqual(len(vocab), 0)
        
        # Test vocabulary stats with empty list
        stats = get_vocabulary_stats([])
        self.assertEqual(stats['total_words'], 0)
        self.assertEqual(stats['unique_words'], 0)
        self.assertEqual(stats['avg_words_per_description'], 0)


if __name__ == '__main__':
    unittest.main()
