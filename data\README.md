# Dataset Directory

This directory should contain the wine dataset required for the machine learning models.

## Required File

**File Name**: `wine_data.csv`

## Expected Format

The CSV file should contain the following columns:

| Column | Type | Description |
|--------|------|-------------|
| `description` | string | Textual description of the wine |
| `variety` | string | Wine variety/type (target variable) |

## Example Data Structure

```csv
description,variety
"This wine has a rich full-bodied flavor with notes of cherry and oak",Pinot Noir
"A light crisp wine with citrus undertones and a clean finish",Sauvignon Blanc
"Bold and complex featuring dark fruit flavors and spicy notes",Cabernet Sauvignon
```

## Data Requirements

- The dataset should contain at least 1000+ samples for meaningful results
- Text descriptions should be in English
- Wine varieties should be consistent in naming
- The model focuses on the top 10 most common varieties in the dataset

## Data Sources

You can obtain wine datasets from:
- [Wine Reviews Dataset on Kaggle](https://www.kaggle.com/zynicide/wine-reviews)
- [UCI Wine Quality Dataset](https://archive.ics.uci.edu/ml/datasets/wine+quality)
- Custom wine review datasets

## Privacy Note

The actual dataset is not included in this repository due to:
- Size constraints
- Potential licensing restrictions
- Privacy considerations

Please ensure you have the right to use any dataset you place in this directory.
