# Wine Variety Classification: Exploring Machine Learning Algorithms

A comprehensive comparison of machine learning algorithms for wine variety classification based on textual descriptions. This project implements and evaluates three different approaches: Deep Learning (CNN), Naive Bayes, and Support Vector Machine.

## 🍷 Project Overview

This project analyzes wine descriptions to predict wine varieties using natural language processing and machine learning techniques. We compare the performance of three different algorithms:

- **Deep Learning**: Convolutional Neural Network with word embeddings
- **Naive Bayes**: Multinomial Naive Bayes with TF-IDF features
- **Support Vector Machine**: Linear SVM with TF-IDF features

## 📊 Dataset

The project uses a wine dataset containing:
- Wine descriptions (text)
- Wine varieties (target labels)
- Focus on top 10 most common wine varieties

## 🚀 Quick Start

### Prerequisites

```bash
Python 3.7+
pip (Python package manager)
```

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/wine-variety-classification.git
cd wine-variety-classification
```

2. Install required packages:
```bash
pip install -r requirements.txt
```

3. Download the dataset:
   - Place your `wine_data.csv` file in the `data/` directory
   - The CSV should contain columns: `description` and `variety`

### Usage

Run individual algorithms:

```bash
# Deep Learning approach
python deep_learning.py

# Naive Bayes approach
python naive_bayes.py

# Support Vector Machine approach
python support_vector.py
```

## 📁 Project Structure

```
wine-variety-classification/
├── README.md                 # Project documentation
├── requirements.txt          # Python dependencies
├── .gitignore               # Git ignore file
├── LICENSE                  # Project license
├── deep_learning.py         # CNN implementation
├── naive_bayes.py          # Naive Bayes implementation
├── support_vector.py       # SVM implementation
├── data/                   # Dataset directory
│   └── wine_data.csv       # Wine dataset (not included)
├── lib/                    # Utility functions
│   ├── __init__.py
│   └── get_top_xwords.py   # Text preprocessing utilities
├── results/                # Model outputs and results
├── notebooks/              # Jupyter notebooks for analysis
└── tests/                  # Unit tests
```

## 🔧 Algorithms Implemented

### 1. Deep Learning (CNN)
- **File**: `deep_learning.py`
- **Architecture**: Embedding layer + 1D CNN + Dense layers
- **Features**: Word embeddings with 60-dimensional vectors
- **Preprocessing**: Sequence padding to 200 words

### 2. Naive Bayes
- **File**: `naive_bayes.py`
- **Algorithm**: Multinomial Naive Bayes
- **Features**: TF-IDF vectorization
- **Preprocessing**: Count vectorization + TF-IDF transformation

### 3. Support Vector Machine
- **File**: `support_vector.py`
- **Algorithm**: Linear SVM
- **Features**: TF-IDF vectorization
- **Preprocessing**: Count vectorization + TF-IDF transformation

## 📈 Performance Metrics

Each algorithm outputs accuracy percentage on the test set. Results are printed to console after training.

## 🛠️ Dependencies

- **TensorFlow/Keras**: Deep learning framework
- **scikit-learn**: Machine learning algorithms and utilities
- **pandas**: Data manipulation and analysis
- **numpy**: Numerical computing
- **collections**: Data structure utilities

## 📝 Usage Examples

### Running All Models
```bash
# Run all three models sequentially
python deep_learning.py && python naive_bayes.py && python support_vector.py
```

### Customizing Parameters
You can modify hyperparameters in each file:
- `max_review_length`: Maximum sequence length for CNN
- `embedding_vector_length`: Embedding dimensions
- `test_size`: Train/test split ratio
- `epochs`: Training epochs for CNN

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Wine dataset contributors
- TensorFlow and scikit-learn communities
- Open source machine learning community

## 📧 Contact

Your Name - <EMAIL>
Project Link: https://github.com/yourusername/wine-variety-classification

---

⭐ If you found this project helpful, please give it a star!