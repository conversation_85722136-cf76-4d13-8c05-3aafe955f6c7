# Contributing to Wine Variety Classification

Thank you for your interest in contributing to this project! This document provides guidelines for contributing to the Wine Variety Classification project.

## 🚀 Getting Started

### Prerequisites

- Python 3.7 or higher
- Git
- Basic knowledge of machine learning and natural language processing

### Setting Up Development Environment

1. Fork the repository on GitHub
2. Clone your fork locally:
   ```bash
   git clone https://github.com/yourusername/wine-variety-classification.git
   cd wine-variety-classification
   ```

3. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

4. Install dependencies:
   ```bash
   pip install -r requirements.txt
   pip install -e .  # Install in development mode
   ```

5. Install development dependencies:
   ```bash
   pip install pytest pytest-cov black flake8
   ```

## 🔧 Development Guidelines

### Code Style

- Follow PEP 8 style guidelines
- Use meaningful variable and function names
- Add docstrings to all functions and classes
- Keep functions focused and small
- Use type hints where appropriate

### Code Formatting

We use `black` for code formatting:

```bash
# Format all Python files
black .

# Check formatting without making changes
black --check .
```

### Linting

We use `flake8` for linting:

```bash
# Run linting
flake8 .
```

### Testing

- Write unit tests for new functionality
- Ensure all tests pass before submitting
- Aim for good test coverage

```bash
# Run tests
pytest

# Run tests with coverage
pytest --cov=lib --cov-report=html
```

## 📝 Types of Contributions

### 🐛 Bug Reports

When filing a bug report, please include:

- Clear description of the issue
- Steps to reproduce the problem
- Expected vs actual behavior
- Python version and operating system
- Relevant error messages or logs

### 💡 Feature Requests

For feature requests, please provide:

- Clear description of the proposed feature
- Use case and motivation
- Possible implementation approach
- Any relevant examples or references

### 🔧 Code Contributions

#### Areas for Contribution

1. **Algorithm Improvements**
   - Hyperparameter tuning
   - New model architectures
   - Performance optimizations

2. **Data Processing**
   - Better text preprocessing
   - Feature engineering
   - Data augmentation techniques

3. **Evaluation Metrics**
   - Additional evaluation metrics
   - Visualization improvements
   - Cross-validation implementations

4. **Documentation**
   - Code documentation
   - Tutorial notebooks
   - Usage examples

5. **Testing**
   - Unit tests
   - Integration tests
   - Performance benchmarks

#### Pull Request Process

1. Create a feature branch:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. Make your changes:
   - Write clean, well-documented code
   - Add tests for new functionality
   - Update documentation as needed

3. Test your changes:
   ```bash
   pytest
   black --check .
   flake8 .
   ```

4. Commit your changes:
   ```bash
   git add .
   git commit -m "Add: brief description of your changes"
   ```

5. Push to your fork:
   ```bash
   git push origin feature/your-feature-name
   ```

6. Create a Pull Request on GitHub

#### Commit Message Guidelines

- Use the present tense ("Add feature" not "Added feature")
- Use the imperative mood ("Move cursor to..." not "Moves cursor to...")
- Limit the first line to 72 characters or less
- Reference issues and pull requests liberally after the first line

Examples:
- `Add: new text preprocessing function`
- `Fix: memory leak in CNN model training`
- `Update: documentation for installation process`
- `Refactor: simplify vocabulary filtering logic`

## 📋 Project Structure

```
wine-variety-classification/
├── lib/                    # Utility functions
├── data/                   # Dataset directory
├── tests/                  # Unit tests
├── notebooks/              # Jupyter notebooks
├── results/                # Model outputs
├── deep_learning.py        # CNN implementation
├── naive_bayes.py         # Naive Bayes implementation
├── support_vector.py      # SVM implementation
└── requirements.txt       # Dependencies
```

## 🤝 Code of Conduct

### Our Pledge

We are committed to making participation in this project a harassment-free experience for everyone, regardless of age, body size, disability, ethnicity, gender identity and expression, level of experience, nationality, personal appearance, race, religion, or sexual identity and orientation.

### Our Standards

Examples of behavior that contributes to creating a positive environment include:

- Using welcoming and inclusive language
- Being respectful of differing viewpoints and experiences
- Gracefully accepting constructive criticism
- Focusing on what is best for the community
- Showing empathy towards other community members

### Enforcement

Instances of abusive, harassing, or otherwise unacceptable behavior may be reported by contacting the project team. All complaints will be reviewed and investigated promptly and fairly.

## 📞 Getting Help

- Create an issue for bugs or feature requests
- Start a discussion for questions or ideas
- Check existing issues and discussions before creating new ones

## 🙏 Recognition

Contributors will be recognized in the project's README and release notes. We appreciate all forms of contribution, from code to documentation to bug reports!

Thank you for contributing to Wine Variety Classification! 🍷
