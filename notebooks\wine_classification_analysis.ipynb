{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Wine Variety Classification Analysis\n", "\n", "This notebook provides an interactive analysis of the wine variety classification project, comparing the performance of different machine learning algorithms."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Data Loading"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from collections import Counter\n", "import sys\n", "import os\n", "\n", "# Add parent directory to path\n", "sys.path.append('..')\n", "from lib.get_top_xwords import get_vocabulary_stats, clean_text\n", "\n", "# Set style for plots\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the wine dataset\n", "# Note: Make sure wine_data.csv is in the ../data/ directory\n", "try:\n", "    df = pd.read_csv('../data/wine_data.csv')\n", "    print(f\"Dataset loaded successfully with {len(df)} samples\")\n", "    print(f\"Columns: {list(df.columns)}\")\n", "except FileNotFoundError:\n", "    print(\"Dataset not found. Please place wine_data.csv in the data/ directory.\")\n", "    print(\"Creating sample data for demonstration...\")\n", "    \n", "    # Create sample data for demonstration\n", "    sample_data = {\n", "        'description': [\n", "            \"Rich and full-bodied with notes of cherry and oak\",\n", "            \"Light and crisp with citrus undertones\",\n", "            \"Bold and complex with dark fruit flavors\",\n", "            \"Smooth texture with hints of vanilla\",\n", "            \"Fresh and vibrant with tropical fruit notes\"\n", "        ],\n", "        'variety': ['Pinot Noir', 'Sauvignon Blanc', 'Cabernet Sauvignon', 'Chardonnay', 'R<PERSON>ling']\n", "    }\n", "    df = pd.DataFrame(sample_data)\n", "    print(\"Using sample data for demonstration.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Exploratory Data Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Basic dataset information\n", "print(\"Dataset Info:\")\n", "print(f\"Shape: {df.shape}\")\n", "print(f\"\\nFirst few rows:\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Wine variety distribution\n", "variety_counts = df['variety'].value_counts()\n", "print(f\"Number of unique wine varieties: {len(variety_counts)}\")\n", "print(f\"\\nTop 10 most common varieties:\")\n", "print(variety_counts.head(10))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize variety distribution\n", "plt.figure(figsize=(12, 6))\n", "variety_counts.head(10).plot(kind='bar')\n", "plt.title('Top 10 Wine Varieties Distribution')\n", "plt.xlabel('Wine Variety')\n", "plt.ylabel('Count')\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Text Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze text descriptions\n", "description_lengths = df['description'].str.len()\n", "word_counts = df['description'].str.split().str.len()\n", "\n", "print(\"Description Statistics:\")\n", "print(f\"Average character length: {description_lengths.mean():.1f}\")\n", "print(f\"Average word count: {word_counts.mean():.1f}\")\n", "print(f\"Min/Max character length: {description_lengths.min()}/{description_lengths.max()}\")\n", "print(f\"Min/Max word count: {word_counts.min()}/{word_counts.max()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize description lengths\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))\n", "\n", "# Character length distribution\n", "ax1.hist(description_lengths, bins=50, alpha=0.7)\n", "ax1.set_title('Distribution of Description Character Lengths')\n", "ax1.set_xlabel('Character Length')\n", "ax1.set_ylabel('Frequency')\n", "\n", "# Word count distribution\n", "ax2.hist(word_counts, bins=50, alpha=0.7)\n", "ax2.set_title('Distribution of Description Word Counts')\n", "ax2.set_xlabel('Word Count')\n", "ax2.set_ylabel('Frequency')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get vocabulary statistics\n", "vocab_stats = get_vocabulary_stats(df['description'].tolist())\n", "print(\"Vocabulary Statistics:\")\n", "for key, value in vocab_stats.items():\n", "    print(f\"  {key}: {value}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Model Comparison Framework\n", "\n", "This section provides a framework for comparing the three machine learning approaches."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Placeholder for model results comparison\n", "# In practice, you would run each model and collect results\n", "\n", "model_results = {\n", "    'Deep Learning (CNN)': {'accuracy': 0.0, 'training_time': 0.0},\n", "    'Naive <PERSON>': {'accuracy': 0.0, 'training_time': 0.0},\n", "    'Support Vector Machine': {'accuracy': 0.0, 'training_time': 0.0}\n", "}\n", "\n", "print(\"Model Results Comparison:\")\n", "print(\"(Run the individual model scripts to populate these results)\")\n", "for model, results in model_results.items():\n", "    print(f\"{model}: Accuracy = {results['accuracy']:.2%}, Training Time = {results['training_time']:.2f}s\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Next Steps\n", "\n", "1. Run the individual model scripts (`deep_learning.py`, `naive_bayes.py`, `support_vector.py`)\n", "2. Collect and compare results\n", "3. Perform hyperparameter tuning\n", "4. Implement cross-validation\n", "5. Add more sophisticated evaluation metrics (precision, recall, F1-score)\n", "6. Visualize confusion matrices\n", "7. Analyze feature importance for interpretable models"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}