"""
Text preprocessing utilities for wine variety classification.

This module provides functions to filter and process text data,
specifically for extracting the most common words from wine descriptions.
"""

import re
from collections import Counter
from typing import List, Tuple, Dict


def clean_text(text: str) -> str:
    """
    Clean and preprocess text data.
    
    Args:
        text (str): Raw text to clean
        
    Returns:
        str: Cleaned text
    """
    if not isinstance(text, str):
        return ""
    
    # Convert to lowercase
    text = text.lower()
    
    # Remove special characters and digits, keep only letters and spaces
    text = re.sub(r'[^a-zA-Z\s]', '', text)
    
    # Remove extra whitespace
    text = ' '.join(text.split())
    
    return text


def filter_to_top_x(description_list: List[str], 
                   vocab_size: int = 2500, 
                   min_word_length: int = 3) -> Tuple[List[List[int]], List[str]]:
    """
    Filter text descriptions to use only the top X most common words.
    
    Args:
        description_list (List[str]): List of text descriptions
        vocab_size (int): Number of top words to keep in vocabulary
        min_word_length (int): Minimum length of words to consider
        
    Returns:
        Tuple[List[List[int]], List[str]]: 
            - List of descriptions converted to word indices
            - List of vocabulary words
    """
    # Clean all descriptions
    cleaned_descriptions = [clean_text(desc) for desc in description_list]
    
    # Count all words
    word_counter = Counter()
    for description in cleaned_descriptions:
        words = description.split()
        # Filter words by minimum length
        words = [word for word in words if len(word) >= min_word_length]
        word_counter.update(words)
    
    # Get top X most common words
    top_words = [word for word, count in word_counter.most_common(vocab_size)]
    
    # Create word to index mapping (starting from 1, 0 reserved for unknown words)
    word_to_idx = {word: idx + 1 for idx, word in enumerate(top_words)}
    
    # Convert descriptions to sequences of indices
    mapped_descriptions = []
    for description in cleaned_descriptions:
        words = description.split()
        # Convert words to indices, use 0 for unknown words
        indices = [word_to_idx.get(word, 0) for word in words 
                  if len(word) >= min_word_length]
        mapped_descriptions.append(indices)
    
    return mapped_descriptions, top_words


def get_vocabulary_stats(description_list: List[str]) -> Dict[str, int]:
    """
    Get statistics about the vocabulary in the descriptions.
    
    Args:
        description_list (List[str]): List of text descriptions
        
    Returns:
        Dict[str, int]: Dictionary with vocabulary statistics
    """
    cleaned_descriptions = [clean_text(desc) for desc in description_list]
    
    word_counter = Counter()
    total_words = 0
    
    for description in cleaned_descriptions:
        words = description.split()
        word_counter.update(words)
        total_words += len(words)
    
    return {
        'total_words': total_words,
        'unique_words': len(word_counter),
        'avg_words_per_description': total_words / len(description_list) if description_list else 0,
        'most_common_word': word_counter.most_common(1)[0] if word_counter else ('', 0)
    }


if __name__ == "__main__":
    # Example usage
    sample_descriptions = [
        "This wine has a rich, full-bodied flavor with notes of cherry and oak.",
        "A light, crisp wine with citrus undertones and a clean finish.",
        "Bold and complex, featuring dark fruit flavors and spicy notes."
    ]
    
    mapped_list, vocab = filter_to_top_x(sample_descriptions, vocab_size=50)
    stats = get_vocabulary_stats(sample_descriptions)
    
    print("Vocabulary Statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    print(f"\nTop 10 vocabulary words: {vocab[:10]}")
    print(f"Mapped descriptions: {mapped_list}")
